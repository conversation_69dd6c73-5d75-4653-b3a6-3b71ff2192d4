/**
 * Example of how to integrate middleware with your existing Bun server
 * This shows the updated index.ts structure with middleware integration
 */

import { serve, type BunRequest } from "bun";
import { connectDB } from "./config/db";
import { PORT } from "./config/env";
import {
  User<PERSON><PERSON><PERSON><PERSON>ontroller,
  UserSignupController,
  UserRefreshTokenController,
  UserLogoutController,
} from "./controllers/user.controller";
import { AppError } from "./utils/error";
import { ZodError } from "zod/v4";

// Import middleware
import {
  authMiddleware,
  corsMiddleware,
  simpleLoggingMiddleware,
  detailedLoggingMiddleware,
  apiRateLimitMiddleware,
  authRateLimitMiddleware,
  withMiddleware,
  errorBoundaryMiddleware,
  securityHeadersMiddleware,
  type AuthenticatedRequest,
} from "./middleware";

connectDB();

// Configure CORS based on environment
const corsConfig = corsMiddleware({
  origin: process.env.NODE_ENV === "production" 
    ? ["https://yourdomain.com", "https://www.yourdomain.com"] 
    : true, // Allow all origins in development
  credentials: true,
  methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
});

// Choose logging based on environment
const loggingConfig = process.env.NODE_ENV === "development" 
  ? detailedLoggingMiddleware 
  : simpleLoggingMiddleware;

// Create middleware-wrapped handlers
const loginHandler = withMiddleware(
  UserLoginController,
  errorBoundaryMiddleware,
  corsConfig,
  loggingConfig,
  authRateLimitMiddleware
);

const signupHandler = withMiddleware(
  UserSignupController,
  errorBoundaryMiddleware,
  corsConfig,
  loggingConfig,
  authRateLimitMiddleware
);

const refreshTokenHandler = withMiddleware(
  UserRefreshTokenController,
  errorBoundaryMiddleware,
  corsConfig,
  loggingConfig,
  authRateLimitMiddleware
);

const logoutHandler = withMiddleware(
  UserLogoutController,
  errorBoundaryMiddleware,
  corsConfig,
  loggingConfig,
  apiRateLimitMiddleware
);

// Example protected content handler
const protectedContentHandler = withMiddleware(
  async (req: AuthenticatedRequest) => {
    const user = req.user; // User is attached by auth middleware
    
    if (req.method === "GET") {
      return Response.json({
        success: true,
        message: "Content retrieved successfully",
        user: {
          id: user!.id,
          username: user!.username,
          fullName: user!.fullName,
        },
        data: {
          // Your content data here
        },
      });
    }
    
    if (req.method === "POST") {
      const body = await req.json();
      // Handle content creation
      return Response.json({
        success: true,
        message: "Content created successfully",
        data: body,
      });
    }
    
    return Response.json(
      { success: false, message: "Method not allowed" },
      { status: 405 }
    );
  },
  errorBoundaryMiddleware,
  corsConfig,
  loggingConfig,
  apiRateLimitMiddleware,
  authMiddleware // Require authentication
);

// Public content handler (no auth required)
const publicContentHandler = withMiddleware(
  async (req: BunRequest) => {
    return Response.json({
      success: true,
      message: "Public content",
      data: {
        // Public content here
      },
    });
  },
  errorBoundaryMiddleware,
  corsConfig,
  loggingConfig,
  apiRateLimitMiddleware
);

const server = serve({
  port: PORT,
  routes: {
    "/": withMiddleware(
      async () => Response.json({
        message: "Server is running fine, but this is not the correct route to hit.",
      }),
      corsConfig,
      securityHeadersMiddleware
    ),
    
    "/api/status": withMiddleware(
      async () => new Response("OK"),
      corsConfig
    ),

    // Authentication routes
    "/api/v1/auth/login": {
      POST: loginHandler,
      OPTIONS: corsConfig, // Handle preflight
    },
    
    "/api/v1/auth/signup": {
      POST: signupHandler,
      OPTIONS: corsConfig,
    },
    
    "/api/v1/auth/refresh": {
      POST: refreshTokenHandler,
      OPTIONS: corsConfig,
    },
    
    "/api/v1/auth/logout": {
      POST: logoutHandler,
      OPTIONS: corsConfig,
    },

    // Protected content routes
    "/api/v1/user/content": {
      GET: protectedContentHandler,
      POST: protectedContentHandler,
      OPTIONS: corsConfig,
    },

    // Public content routes
    "/api/v1/public/content": {
      GET: publicContentHandler,
      OPTIONS: corsConfig,
    },

    // Catch-all for unmatched API routes
    "/api/*": withMiddleware(
      async () => Response.json(
        { success: false, message: "API endpoint not found" },
        { status: 404 }
      ),
      corsConfig,
      loggingConfig
    ),
  },

  // Fallback for unmatched routes
  fetch: withMiddleware(
    async () => Response.json(
      { success: false, message: "Route not found" },
      { status: 404 }
    ),
    corsConfig,
    securityHeadersMiddleware
  ),

  // Enhanced error handler
  error(err: Error): Response {
    console.error("Caught by Bun.server global error handler:", err);

    let statusCode = 500;
    let message = "Internal Server Error";
    let errors: any[] | undefined = undefined;

    if (err instanceof AppError) {
      statusCode = err.statusCode;
      message = err.message;

      if (!err.isOperational) {
        console.error("Non-operational error (likely a bug):", err.stack);
      }
    } else if ((err as any).code === 11000) {
      statusCode = 409;
      message = "User already exists.";
      if ((err as any).keyValue) {
        message += ` (Key: ${Object.keys((err as any).keyValue).join(",")})`;
      }
    } else if (err instanceof ZodError) {
      statusCode = 400;
      message = "Validation failed";
      errors = err.issues.map((e) => ({
        path: e.path.join("."),
        message: e.message,
      }));
    } else {
      if (process.env.NODE_ENV === "production") {
        message = "An unexpected server error occurred.";
      } else {
        message = `An unexpected server error occurred: ${err.message}`;
      }
    }

    return Response.json(
      {
        success: false,
        message: message,
        errors: errors,
        ...(process.env.NODE_ENV !== "production" && { stack: err.stack }),
      },
      {
        status: statusCode,
        headers: { 
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
        },
      }
    );
  },
});

console.log(`🚀 Server running at ${server.url}`);
console.log(`📝 Environment: ${process.env.NODE_ENV || "development"}`);
console.log(`🔒 CORS configured for: ${process.env.NODE_ENV === "production" ? "production domains" : "all origins"}`);

export default server;
