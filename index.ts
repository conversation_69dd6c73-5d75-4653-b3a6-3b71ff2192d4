import { serve, type BunRequest } from "bun";
import { connectDB } from "./config/db";
import { PORT } from "./config/env";
import {
  UserLoginController,
  UserSignupController,
} from "./controllers/user.controller";
import { AppError } from "./utils/error";
import { ZodError } from "zod/v4";

connectDB();

const server = serve({
  port: PORT,
  // `routes` requires Bun v1.2.3+
  routes: {
    "/": Response.json(
      {
        message:
          "Server is running fine, but this not the correct route to hit.",
      },
      { status: 200 },
    ),
    "/api/status": new Response("OK"),
    // Static routes
    "/api/v1/login": {
      POST: async (req: BunRequest<"/api/v1/login">): Promise<Response> => {
        const res = UserLoginController(req);
        return res;
      },
    },
    "/api/v1/signup": {
      POST: async (req: BunRequest<"/api/v1/signup">): Promise<Response> => {
        const res = UserSignupController(req);
        return res;
      },
    },
    "/api/v1/user/content":{
    POST:,
    GET: ,
    },

    // Dynamic routes
    // "/users/:id": (req) => {
    //   return new Response(`Hello User ${req.params.id}!`);
    // },

    // Per-HTTP method handlers
    // "/api/posts": {
    //   GET: () => new Response("List posts"),
    //   POST: async (req) => {
    //     const body = await req.json();
    //     return Response.json({ created: true, ...body });
    //   },
    // },

    // Wildcard route for all routes that start with "/api/" and aren't otherwise matched
    "/api/*": Response.json({ message: "Not found" }, { status: 404 }),

    // Redirect from /blog/hello to /blog/hello/world
    "/blog/hello": Response.redirect("/blog/hello/world"),

    // Serve a file by buffering it in memory
    // "/favicon.ico": new Response(await Bun.file("./favicon.ico").bytes(), {
    //   headers: {
    //     "Content-Type": "image/x-icon",
    //   },
    // }),
  },

  // (optional) fallback for unmatched routes:
  // Required if Bun's version < 1.2.3
  fetch(req) {
    return new Response("Not Found", { status: 404 });
  },

  error(err: Error): Response {
    console.error("Caught by Bun.server global error handler:", err);

    let statusCode = 500;
    let message = "Internal Server Error";
    let errors: any[] | undefined = undefined;

    if (err instanceof AppError) {
      statusCode = err.statusCode;
      message = err.message;

      if (!err.isOperational) {
        console.error("Non-operational error (likely a bug):", err.stack);
      }
    } else if ((err as any).code === 11000) {
      statusCode = 409;
      message = "User already exists.";
      if ((err as any).keyValue) {
        message += `(Key: ${Object.keys((err as any).keyValue).join(",")})`;
      }
    } else if (err instanceof ZodError) {
      statusCode = 400;
      message = "Validation failed";
      errors = err.issues.map((e) => ({
        path: e.path.join("."),
        message: e.message,
      }));
    } else {
      if (process.env.NODE_ENV === "production") {
        message = "An unexpected server error occured.";
      } else {
        message = `An unexpected server error occured: ${err.message}`;
      }
    }

    return Response.json(
      {
        success: false,
        message: message,
        errors: errors,
      },
      {
        status: statusCode,
        headers: { "Content-Type": "application/json" },
      },
    );
  },
});

console.log(`🚀 Server running at ${server.url}`);
