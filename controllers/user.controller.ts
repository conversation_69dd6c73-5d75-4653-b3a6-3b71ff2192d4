import type { BunRequest } from "bun";
import {
  UserLoginSchema,
  UserSignupSchema,
  type UserLoginInputType,
  type UserSignupInputType,
} from "../schema/user.schema";
import { UserLoginService, UserSignupService } from "../services/user.service";
import { ZodError } from "zod/v4";

export async function UserLoginController(req: BunRequest): Promise<Response> {
  try {
    const body = await req.json();
    const cookies = req.cookies;

    const validatedLoginData: UserLoginInputType = UserLoginSchema.parse(body);

    const { user, token } = await UserLoginService(validatedLoginData);

    cookies.set("user_id", token, {
      maxAge: 60 * 60 * 24 * 7,
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "none",
    });

    return Response.json(
      {
        success: true,
        message: "Login successfull.",
        user: {
          id: user._id,
          fullName: user.fullName,
          username: user.username,
        },
        token,
      },
      {
        status: 200,
        statusText: "Login Approved",
        headers: {
          "X-Auth-Token": token,
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if (error instanceof ZodError) {
      return Response.json(
        {
          success: false,
          message: "Validation failed",
          errors: error.issues.map((issue) => ({
            path: issue.path.join("."),
            message: issue.message,
          })),
        },
        { status: 400 }
      );
    }
    throw error;
  }
}

export async function UserSignupController(req: BunRequest): Promise<Response> {
  try {
    const body = await req.json();
    const cookies = req.cookies;

    const validatedUserData: UserSignupInputType = UserSignupSchema.parse(body);

    const { user, token } = await UserSignupService(validatedUserData);

    cookies.set("user_id", token, {
      maxAge: 60 * 60 * 24 * 7,
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "none",
    });

    return Response.json({
      message: "Signup successfull.",
      user: {
        id: user._id,
        fullName: user.fullName,
        username: user.username,
      },
      token,
    });
  } catch (error) {
    if (error instanceof ZodError) {
      return Response.json(
        {
          success: false,
          message: "Validation Failed",
          errors: error.issues.map((issue) => ({
            path: issue.path.join("."),
            message: issue.message,
          })),
        },
        { status: 400 }
      );
    }
    throw error;
  }
}
