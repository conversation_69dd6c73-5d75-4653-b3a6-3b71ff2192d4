import type { BunRequest } from "bun";
import {
  UserLoginSchema,
  UserSignupSchema,
  type UserLoginInputType,
  type UserSignupInputType,
} from "../schema/user.schema";
import {
  refreshUserToken,
  UserLoginService,
  UserSignupService,
} from "../services/user.service";
import { ZodError } from "zod/v4";
import { AppError } from "../utils/error";

export async function UserLoginController(req: BunRequest): Promise<Response> {
  try {
    const body = await req.json();
    const cookies = req.cookies;

    const validatedLoginData: UserLoginInputType = UserLoginSchema.parse(body);

    const { user, accessToken, refreshToken } = await UserLoginService(
      validatedLoginData
    );

    cookies.set("refreshToken", refreshToken, {
      maxAge: 60 * 60 * 24 * 7,
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "none",
    });

    return Response.json(
      {
        success: true,
        message: "Login successfull.",
        user: {
          id: user._id,
          fullName: user.fullName,
          username: user.username,
        },
        accessToken,
      },
      {
        status: 200,
        statusText: "Login Approved",
        headers: {
          "Access-Control-Allow-Origin": "*",
          // "Access-Control-Allow-Credentials": "true",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if (error instanceof ZodError) {
      return Response.json(
        {
          success: false,
          message: "Validation failed",
          errors: error.issues.map((issue) => ({
            path: issue.path.join("."),
            message: issue.message,
          })),
        },
        { status: 400 }
      );
    }
    throw error;
  }
}

export async function UserSignupController(req: BunRequest): Promise<Response> {
  try {
    const body = await req.json();
    const cookies = req.cookies;

    const validatedUserData: UserSignupInputType = UserSignupSchema.parse(body);

    const { user, accessToken, refreshToken } = await UserSignupService(
      validatedUserData
    );

    cookies.set("refreshToken", refreshToken, {
      maxAge: 60 * 60 * 24 * 7,
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "none",
    });

    return Response.json(
      {
        message: "Signup successfull.",
        user: {
          id: user._id,
          fullName: user.fullName,
          username: user.username,
        },
        accessToken,
      },
      {
        status: 201,
        headers: {
          "Access-Control-Allow-Origin": "*",
          // "Access-Control-Allow-Credentials": "true",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if (error instanceof ZodError) {
      return Response.json(
        {
          success: false,
          message: "Validation Failed",
          errors: error.issues.map((issue) => ({
            path: issue.path.join("."),
            message: issue.message,
          })),
        },
        { status: 400 }
      );
    }
    throw error;
  }
}

export async function UserRefreshTokenController(
  req: BunRequest
): Promise<Response> {
  try {
    const cookies = req.cookies;
    const oldRefreshToken = cookies.get("refreshToken");

    if (!oldRefreshToken) {
      throw new AppError("Please log in again: Token not provided", 401);
    }

    const { accessToken, refreshToken: newRefreshToken } =
      await refreshUserToken(oldRefreshToken);

    cookies.set("refreshToken", newRefreshToken, {
      maxAge: 60 * 60 * 24 * 7,
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      path: "/api/v1/auth/refresh",
    });

    return Response.json(
      {
        success: true,
        message: "Token refreshed.",
        accessToken,
      },
      {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          // "Access-Control-Allow-Credentials": "true",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if(error instanceof AppError){
      return Response.json({success:false, message: error.message}, error.statusCode)
    }

  }
}
