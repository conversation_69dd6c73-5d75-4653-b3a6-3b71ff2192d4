import type { BunRequest } from "bun";
import { ContentSchema, type ContentTypes } from "../schema/content.schema";
import { ZodError } from "zod/v4";

export async function ContentCreateController(
  req: BunRequest,
): Promise<Response> {
  try {
    const body = await req.json();
    const cookies = req.cookies;

    const validContentData: ContentTypes = ContentSchema.parse(body);

    return Response.json(
      { success: true, message: "Link saved successfull." },
      {
        status: 200,
        statusText: "Link saved.",
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0,",
        },
      },
    );
  } catch (error) {
    if (error instanceof ZodError) {
      return Response.json(
        {
          success: false,
          message: "Validation failed",
          errors: error.issues.map((issue) => ({
            path: issue.path.join("."),
            message: issue.message,
          })),
        },
        { status: 400 },
      );
    }
    throw error;
  }
}
