import type { BunRequest } from "bun";

export interface CorsOptions {
  origin?: string | string[] | boolean;
  methods?: string[];
  allowedHeaders?: string[];
  credentials?: boolean;
  maxAge?: number;
}

const defaultCorsOptions: CorsOptions = {
  origin: "*",
  methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
  allowedHeaders: [
    "Content-Type",
    "Authorization",
    "X-Requested-With",
    "Accept",
    "Origin",
  ],
  credentials: true,
  maxAge: 86400, // 24 hours
};

/**
 * CORS middleware for handling Cross-Origin Resource Sharing
 */
export function corsMiddleware(options: CorsOptions = {}) {
  const config = { ...defaultCorsOptions, ...options };

  return async function (
    req: BunRequest,
    next: () => Promise<Response>
  ): Promise<Response> {
    const origin = req.headers.get("Origin");
    const method = req.method;

    // Handle preflight OPTIONS request
    if (method === "OPTIONS") {
      const headers = new Headers();

      // Set Access-Control-Allow-Origin
      if (config.origin === true) {
        headers.set("Access-Control-Allow-Origin", origin || "*");
      } else if (typeof config.origin === "string") {
        headers.set("Access-Control-Allow-Origin", config.origin);
      } else if (Array.isArray(config.origin)) {
        if (origin && config.origin.includes(origin)) {
          headers.set("Access-Control-Allow-Origin", origin);
        }
      } else {
        headers.set("Access-Control-Allow-Origin", "*");
      }

      // Set other CORS headers
      if (config.methods) {
        headers.set("Access-Control-Allow-Methods", config.methods.join(", "));
      }

      if (config.allowedHeaders) {
        headers.set(
          "Access-Control-Allow-Headers",
          config.allowedHeaders.join(", ")
        );
      }

      if (config.credentials) {
        headers.set("Access-Control-Allow-Credentials", "true");
      }

      if (config.maxAge) {
        headers.set("Access-Control-Max-Age", config.maxAge.toString());
      }

      return new Response(null, {
        status: 204,
        headers,
      });
    }

    // For non-preflight requests, get the response first
    const response = await next();

    // Clone the response to modify headers
    const newResponse = new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: new Headers(response.headers),
    });

    // Add CORS headers to the response
    if (config.origin === true) {
      newResponse.headers.set("Access-Control-Allow-Origin", origin || "*");
    } else if (typeof config.origin === "string") {
      newResponse.headers.set("Access-Control-Allow-Origin", config.origin);
    } else if (Array.isArray(config.origin)) {
      if (origin && config.origin.includes(origin)) {
        newResponse.headers.set("Access-Control-Allow-Origin", origin);
      }
    } else {
      newResponse.headers.set("Access-Control-Allow-Origin", "*");
    }

    if (config.credentials) {
      newResponse.headers.set("Access-Control-Allow-Credentials", "true");
    }

    // Expose headers that the client can access
    newResponse.headers.set(
      "Access-Control-Expose-Headers",
      "X-Auth-Token, Server-Version"
    );

    return newResponse;
  };
}

/**
 * Development CORS middleware with permissive settings
 */
export const devCorsMiddleware = corsMiddleware({
  origin: true, // Allow any origin
  credentials: true,
  methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
  allowedHeaders: [
    "Content-Type",
    "Authorization",
    "X-Requested-With",
    "Accept",
    "Origin",
    "X-Auth-Token",
  ],
});

/**
 * Production CORS middleware with restrictive settings
 */
export const prodCorsMiddleware = corsMiddleware({
  origin: [
    "https://yourdomain.com",
    "https://www.yourdomain.com",
    // Add your production domains here
  ],
  credentials: true,
  methods: ["GET", "POST", "PUT", "DELETE", "PATCH"],
  allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
});
