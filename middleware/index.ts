import type { BunRequest } from "bun";

// Export all middleware
export * from "./auth.middleware";
export * from "./cors.middleware";
export * from "./logging.middleware";
export * from "./rateLimit.middleware";

// Middleware composition types
export type MiddlewareFunction = (
  req: BunRequest,
  next: () => Promise<Response>
) => Promise<Response>;

export type RouteHandler = (req: BunRequest) => Promise<Response>;

/**
 * Compose multiple middleware functions into a single handler
 */
export function composeMiddleware(
  ...middlewares: MiddlewareFunction[]
): (handler: RouteHandler) => RouteHandler {
  return function (handler: RouteHandler): RouteHandler {
    return async function (req: BunRequest): Promise<Response> {
      let index = 0;

      async function next(): Promise<Response> {
        if (index >= middlewares.length) {
          return await handler(req);
        }

        const middleware = middlewares[index++];
        return await middleware(req, next);
      }

      return await next();
    };
  };
}

/**
 * Apply middleware to a route handler
 */
export function withMiddleware(
  handler: <PERSON><PERSON><PERSON><PERSON>,
  ...middlewares: MiddlewareFunction[]
): RouteHandler {
  return composeMiddleware(...middlewares)(handler);
}

/**
 * Create a middleware pipeline
 */
export class MiddlewarePipeline {
  private middlewares: MiddlewareFunction[] = [];

  use(middleware: MiddlewareFunction): this {
    this.middlewares.push(middleware);
    return this;
  }

  execute(handler: RouteHandler): RouteHandler {
    return composeMiddleware(...this.middlewares)(handler);
  }

  clone(): MiddlewarePipeline {
    const pipeline = new MiddlewarePipeline();
    pipeline.middlewares = [...this.middlewares];
    return pipeline;
  }
}

/**
 * Common middleware combinations
 */

// Basic middleware stack for all routes
export const basicMiddleware = composeMiddleware(
  // Add basic middleware here
);

// API middleware stack
export const apiMiddleware = composeMiddleware(
  // Add API-specific middleware here
);

// Auth-protected middleware stack
export const protectedMiddleware = composeMiddleware(
  // Add auth middleware here
);

/**
 * Error boundary middleware
 */
export function errorBoundaryMiddleware(
  req: BunRequest,
  next: () => Promise<Response>
): Promise<Response> {
  return next().catch((error: Error) => {
    console.error("Unhandled error in middleware:", error);
    
    return Response.json(
      {
        success: false,
        message: process.env.NODE_ENV === "production" 
          ? "Internal server error" 
          : error.message,
        ...(process.env.NODE_ENV !== "production" && { stack: error.stack }),
      },
      { status: 500 }
    );
  });
}

/**
 * Request validation middleware
 */
export function requestValidationMiddleware(
  req: BunRequest,
  next: () => Promise<Response>
): Promise<Response> {
  // Add request validation logic here
  // For example, check content-type for POST requests
  if (req.method === "POST" || req.method === "PUT" || req.method === "PATCH") {
    const contentType = req.headers.get("Content-Type");
    if (!contentType || !contentType.includes("application/json")) {
      return Promise.resolve(
        Response.json(
          {
            success: false,
            message: "Content-Type must be application/json",
          },
          { status: 400 }
        )
      );
    }
  }

  return next();
}

/**
 * Security headers middleware
 */
export function securityHeadersMiddleware(
  req: BunRequest,
  next: () => Promise<Response>
): Promise<Response> {
  return next().then((response) => {
    const newResponse = new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: new Headers(response.headers),
    });

    // Add security headers
    newResponse.headers.set("X-Content-Type-Options", "nosniff");
    newResponse.headers.set("X-Frame-Options", "DENY");
    newResponse.headers.set("X-XSS-Protection", "1; mode=block");
    newResponse.headers.set(
      "Strict-Transport-Security",
      "max-age=31536000; includeSubDomains"
    );
    newResponse.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");

    return newResponse;
  });
}
