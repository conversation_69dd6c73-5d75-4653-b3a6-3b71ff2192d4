/**
 * Example usage of middleware with Bun server
 * This file shows how to integrate the middleware system with your existing server
 */

import type { BunRequest } from "bun";
import {
  authMiddleware,
  corsMiddleware,
  simpleLoggingMiddleware,
  apiRateLimitMiddleware,
  authRateLimitMiddleware,
  withMiddleware,
  composeMiddleware,
  errorBoundaryMiddleware,
  securityHeadersMiddleware,
} from "./index";

// Import your controllers
import {
  UserLoginController,
  UserSignupController,
  UserRefreshTokenController,
  UserLogoutController,
} from "../controllers/user.controller";

/**
 * Example: Protected route with authentication
 */
export const protectedUserContentHandler = withMiddleware(
  async (req: BunRequest) => {
    // Your protected route logic here
    return Response.json({
      success: true,
      message: "Protected content accessed successfully",
      user: (req as any).user, // User will be attached by auth middleware
    });
  },
  errorBoundaryMiddleware,
  corsMiddleware(),
  simpleLoggingMiddleware,
  apiRateLimitMiddleware,
  authMiddleware // This will ensure user is authenticated
);

/**
 * Example: Public route with basic middleware
 */
export const publicContentHandler = withMiddleware(
  async (req: BunRequest) => {
    return Response.json({
      success: true,
      message: "Public content",
    });
  },
  errorBoundaryMiddleware,
  corsMiddleware(),
  simpleLoggingMiddleware,
  apiRateLimitMiddleware
);

/**
 * Example: Auth routes with rate limiting
 */
export const loginHandler = withMiddleware(
  UserLoginController,
  errorBoundaryMiddleware,
  corsMiddleware(),
  simpleLoggingMiddleware,
  authRateLimitMiddleware // Stricter rate limiting for auth
);

export const signupHandler = withMiddleware(
  UserSignupController,
  errorBoundaryMiddleware,
  corsMiddleware(),
  simpleLoggingMiddleware,
  authRateLimitMiddleware
);

export const refreshTokenHandler = withMiddleware(
  UserRefreshTokenController,
  errorBoundaryMiddleware,
  corsMiddleware(),
  simpleLoggingMiddleware,
  authRateLimitMiddleware
);

export const logoutHandler = withMiddleware(
  UserLogoutController,
  errorBoundaryMiddleware,
  corsMiddleware(),
  simpleLoggingMiddleware,
  apiRateLimitMiddleware
);

/**
 * Example: Global middleware for all routes
 */
export const globalMiddleware = composeMiddleware(
  errorBoundaryMiddleware,
  securityHeadersMiddleware,
  corsMiddleware({
    origin: process.env.NODE_ENV === "production" 
      ? ["https://yourdomain.com"] 
      : true,
    credentials: true,
  }),
  simpleLoggingMiddleware
);

/**
 * Example: How to apply global middleware to your server
 * 
 * In your index.ts, you would modify your routes like this:
 * 
 * const server = serve({
 *   port: PORT,
 *   routes: {
 *     "/api/v1/login": {
 *       POST: loginHandler,
 *     },
 *     "/api/v1/signup": {
 *       POST: signupHandler,
 *     },
 *     "/api/v1/auth/refresh": {
 *       POST: refreshTokenHandler,
 *     },
 *     "/api/v1/auth/logout": {
 *       POST: logoutHandler,
 *     },
 *     "/api/v1/user/content": {
 *       GET: protectedUserContentHandler,
 *       POST: protectedUserContentHandler,
 *     },
 *     "/api/v1/public/content": {
 *       GET: publicContentHandler,
 *     },
 *   },
 *   // ... rest of your server config
 * });
 */

/**
 * Example: Custom middleware for specific needs
 */
export function userOwnershipMiddleware(
  req: BunRequest,
  next: () => Promise<Response>
): Promise<Response> {
  // Example: Check if user owns the resource they're trying to access
  const url = new URL(req.url);
  const userId = url.pathname.split('/').pop(); // Get user ID from URL
  const authenticatedUser = (req as any).user;

  if (authenticatedUser && userId && authenticatedUser.id !== userId) {
    return Promise.resolve(
      Response.json(
        {
          success: false,
          message: "Access denied: You can only access your own resources",
        },
        { status: 403 }
      )
    );
  }

  return next();
}

/**
 * Example: Content-specific protected route
 */
export const userSpecificContentHandler = withMiddleware(
  async (req: BunRequest) => {
    const user = (req as any).user;
    return Response.json({
      success: true,
      message: `Content for user ${user.username}`,
      data: {
        // User-specific content here
      },
    });
  },
  errorBoundaryMiddleware,
  corsMiddleware(),
  simpleLoggingMiddleware,
  apiRateLimitMiddleware,
  authMiddleware,
  userOwnershipMiddleware // Custom middleware for ownership check
);
