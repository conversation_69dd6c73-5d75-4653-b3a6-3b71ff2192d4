import jwt, { type JwtPayload } from "jsonwebtoken";
import { JWT_ACCESS_SECRET } from "../config/env";
import { User } from "../models/user.model";
import { AppError } from "../utils/error";
import type { BunRequest } from "bun";

export interface AuthenticatedRequest extends BunRequest {
  user?: {
    id: string;
    fullName: string;
    username: string;
  };
}

export interface MiddlewareContext {
  req: AuthenticatedRequest;
  next: () => Promise<Response>;
}

/**
 * Authentication middleware that verifies JWT access tokens
 * Supports both Authorization header and cookie-based authentication
 */
export async function authMiddleware(
  req: AuthenticatedRequest,
  next: () => Promise<Response>
): Promise<Response> {
  try {
    let token: string | undefined;

    // Check Authorization header first
    const authHeader = req.headers.get("Authorization");
    if (authHeader && authHeader.startsWith("Bearer ")) {
      token = authHeader.substring(7);
    }

    // Fallback to cookie if no Authorization header
    if (!token) {
      token = req.cookies.get("accessToken");
    }

    if (!token) {
      return Response.json(
        {
          success: false,
          message: "Access token required. Please log in.",
        },
        { status: 401 }
      );
    }

    // Verify the token
    let decoded: JwtPayload & { id: string };
    try {
      const payload = jwt.verify(token, JWT_ACCESS_SECRET);
      if (typeof payload === "string") {
        throw new Error("Invalid token format");
      }
      decoded = payload as JwtPayload & { id: string };
    } catch (error) {
      return Response.json(
        {
          success: false,
          message: "Invalid or expired access token. Please log in again.",
        },
        { status: 401 }
      );
    }

    if (!decoded.id) {
      return Response.json(
        {
          success: false,
          message: "Invalid token payload.",
        },
        { status: 401 }
      );
    }

    // Fetch user from database
    const user = await User.findById(decoded.id);
    if (!user) {
      return Response.json(
        {
          success: false,
          message: "User not found. Please log in again.",
        },
        { status: 401 }
      );
    }

    // Attach user to request
    req.user = {
      id: user._id.toString(),
      fullName: user.fullName,
      username: user.username,
    };

    // Continue to next middleware/handler
    return await next();
  } catch (error) {
    console.error("Auth middleware error:", error);
    return Response.json(
      {
        success: false,
        message: "Authentication failed.",
      },
      { status: 500 }
    );
  }
}

/**
 * Optional authentication middleware - doesn't fail if no token provided
 * Useful for routes that work for both authenticated and unauthenticated users
 */
export async function optionalAuthMiddleware(
  req: AuthenticatedRequest,
  next: () => Promise<Response>
): Promise<Response> {
  try {
    let token: string | undefined;

    // Check Authorization header first
    const authHeader = req.headers.get("Authorization");
    if (authHeader && authHeader.startsWith("Bearer ")) {
      token = authHeader.substring(7);
    }

    // Fallback to cookie if no Authorization header
    if (!token) {
      token = req.cookies.get("accessToken");
    }

    if (token) {
      try {
        const payload = jwt.verify(token, JWT_ACCESS_SECRET);
        if (typeof payload !== "string") {
          const decoded = payload as JwtPayload & { id: string };
          if (decoded.id) {
            const user = await User.findById(decoded.id);
            if (user) {
              req.user = {
                id: user._id.toString(),
                fullName: user.fullName,
                username: user.username,
              };
            }
          }
        }
      } catch (error) {
        // Silently ignore token errors in optional auth
        console.warn("Optional auth token verification failed:", error);
      }
    }

    return await next();
  } catch (error) {
    console.error("Optional auth middleware error:", error);
    return await next(); // Continue even if there's an error
  }
}
