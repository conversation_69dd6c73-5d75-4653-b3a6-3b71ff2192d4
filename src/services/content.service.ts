import { Types } from "mongoose";
import { Content } from "../models/content.model";
import { User } from "../models/user.model";
import type { ContentTypes } from "../schema/content.schema";
import {
  BadRequestError,
  NotFoundError,
  UnathorizedError,
} from "../utils/error";

export async function CreateContentService(
  id: string | undefined,
  body: ContentTypes
) {
  const existingUser = await User.findById(id);

  if (!existingUser) {
    throw new BadRequestError("User not found.");
  }

  const contentData = new Content(body);
  await contentData.save();

  const { userId: _, ...contentDataFiltered } = contentData.toObject();

  return contentDataFiltered;
}

export async function GetUserContentService(id: string | undefined) {
  const existingUser = await User.findById(id);

  if (!existingUser) {
    throw new BadRequestError("User not found");
  }

  const userContentData = await Content.find({ userId: id })
    .select("-userId -__v")
    .lean()
    .exec();

  return userContentData;
}

export async function UpdateUserContentService(
  contentId: string,
  userId: string,
  updateData: ContentTypes
) {
  if (!Types.ObjectId.isValid(contentId)) {
    throw new BadRequestError("Invalid Content Id format.");
  }
  if (!Types.ObjectId.isValid(userId)) {
    throw new BadRequestError("Invalid User Id format.");
  }

  const dataToUpdate: any = { ...updateData };
  if (dataToUpdate.tags) {
    dataToUpdate.tags = dataToUpdate.tags.map((tagId: string) => {
      if (!Types.ObjectId.isValid(tagId)) {
        throw new BadRequestError(`Invalid Tag Id format: ${tagId}`);
      }
      return new Types.ObjectId(tagId);
    });
  }

  const updatedContent = await Content.findOneAndUpdate(
    { _id: new Types.ObjectId(contentId), userId: new Types.ObjectId(userId) },
    { $set: dataToUpdate },
    { new: true, runValidators: true }
  );

  if (!updatedContent) {
    const contentExists = await Content.findById(contentId);
    if (!contentExists) {
      throw new NotFoundError("Content not found.");
    } else {
      throw new UnathorizedError(
        "You are not authorized to update this content."
      );
    }
  }

  return updatedContent.toObject();
}

export async function GetContentService(contentId: string) {
  const contentData = await Content.findById(contentId);

  if (!contentData) {
    throw new NotFoundError("Content not found.");
  }

  return contentData.toObject();
}
