import { Types } from "mongoose";
import { Link } from "../models/link.model";
import { Content } from "../models/content.model";
import { User } from "../models/user.model";
import type { LinkInput } from "../schema/link.schema";
import {
  BadRequestError,
  NotFoundError,
  UnathorizedError,
  ConflictError,
} from "../utils/error";
import { nanoid } from "nanoid";

/**
 * Create a shareable link for content
 */
export async function CreateShareableLinkService(
  userId: string,
  linkData: LinkInput
) {
  try {
    // Validate user exists
    const user = await User.findById(userId);
    if (!user) {
      throw new BadRequestError("User not found.");
    }

    // Validate content exists and belongs to user
    const content = await Content.findOne({
      _id: linkData.contentId,
      userId: userId,
    });
    if (!content) {
      throw new NotFoundError("Content not found or you don't have permission to share it.");
    }

    // Check if an active link already exists for this content
    const existingLink = await Link.findOne({
      contentId: linkData.contentId,
      userId: userId,
      isActive: true,
    });

    if (existingLink) {
      throw new ConflictError("An active shareable link already exists for this content.");
    }

    // Create new shareable link
    const newLink = new Link({
      shortCode: nanoid(12), // Generate unique short code
      contentId: linkData.contentId,
      userId: userId,
      expiresAt: linkData.expiresAt,
      maxAccesses: linkData.maxAccesses,
      isActive: true,
      currentAccesses: 0,
    });

    await newLink.save();

    // Populate content details for response
    const populatedLink = await Link.findById(newLink._id)
      .populate('contentId', 'title type link')
      .lean();

    return populatedLink;
  } catch (error) {
    if (error instanceof BadRequestError || 
        error instanceof NotFoundError || 
        error instanceof ConflictError) {
      throw error;
    }
    throw new BadRequestError("Failed to create shareable link.");
  }
}

/**
 * Get all shareable links for a user
 */
export async function GetUserShareableLinksService(
  userId: string,
  page: number = 1,
  limit: number = 20,
  isActive?: boolean
) {
  try {
    const skip = (page - 1) * limit;
    
    // Build query
    const query: any = { userId: new Types.ObjectId(userId) };
    if (isActive !== undefined) {
      query.isActive = isActive;
    }

    // Get links with pagination
    const [links, totalCount] = await Promise.all([
      Link.find(query)
        .populate('contentId', 'title type link tags')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Link.countDocuments(query)
    ]);

    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return {
      links,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNextPage,
        hasPrevPage,
        limit
      }
    };
  } catch (error) {
    throw new BadRequestError("Failed to fetch shareable links.");
  }
}

/**
 * Get shareable link by short code (public access)
 */
export async function GetShareableLinkByCodeService(shortCode: string) {
  try {
    const link = await Link.findOne({ shortCode })
      .populate('contentId', 'title type link tags')
      .populate('userId', 'fullName username');

    if (!link) {
      throw new NotFoundError("Shareable link not found.");
    }

    // Check if link is active
    if (!link.isActive) {
      throw new BadRequestError("This shareable link has been deactivated.");
    }

    // Check if link has expired
    if (link.expiresAt && new Date() > link.expiresAt) {
      // Automatically deactivate expired link
      await Link.findByIdAndUpdate(link._id, { isActive: false });
      throw new BadRequestError("This shareable link has expired.");
    }

    // Check if max accesses reached
    if (link.maxAccesses && link.currentAccesses >= link.maxAccesses) {
      // Automatically deactivate link that reached max accesses
      await Link.findByIdAndUpdate(link._id, { isActive: false });
      throw new BadRequestError("This shareable link has reached its maximum access limit.");
    }

    // Increment access count
    await Link.findByIdAndUpdate(link._id, { 
      $inc: { currentAccesses: 1 } 
    });

    return {
      content: link.contentId,
      sharedBy: {
        fullName: (link.userId as any).fullName,
        username: (link.userId as any).username,
      },
      accessInfo: {
        currentAccesses: link.currentAccesses + 1,
        maxAccesses: link.maxAccesses,
        expiresAt: link.expiresAt,
        createdAt: link.createdAt,
      }
    };
  } catch (error) {
    if (error instanceof NotFoundError || error instanceof BadRequestError) {
      throw error;
    }
    throw new BadRequestError("Failed to access shareable link.");
  }
}

/**
 * Update shareable link settings
 */
export async function UpdateShareableLinkService(
  linkId: string,
  userId: string,
  updateData: Partial<LinkInput>
) {
  try {
    if (!Types.ObjectId.isValid(linkId)) {
      throw new BadRequestError("Invalid link ID format.");
    }

    // Find link and verify ownership
    const link = await Link.findOne({
      _id: linkId,
      userId: userId,
    });

    if (!link) {
      throw new NotFoundError("Shareable link not found or you don't have permission to modify it.");
    }

    // Prepare update data
    const updateFields: any = {};
    if (updateData.expiresAt !== undefined) {
      updateFields.expiresAt = updateData.expiresAt;
    }
    if (updateData.maxAccesses !== undefined) {
      updateFields.maxAccesses = updateData.maxAccesses;
    }
    if (updateData.isActive !== undefined) {
      updateFields.isActive = updateData.isActive;
    }

    const updatedLink = await Link.findByIdAndUpdate(
      linkId,
      { $set: updateFields },
      { new: true, runValidators: true }
    ).populate('contentId', 'title type link');

    return updatedLink;
  } catch (error) {
    if (error instanceof BadRequestError || error instanceof NotFoundError) {
      throw error;
    }
    throw new BadRequestError("Failed to update shareable link.");
  }
}

/**
 * Delete/Deactivate shareable link
 */
export async function DeleteShareableLinkService(linkId: string, userId: string) {
  try {
    if (!Types.ObjectId.isValid(linkId)) {
      throw new BadRequestError("Invalid link ID format.");
    }

    const deletedLink = await Link.findOneAndDelete({
      _id: linkId,
      userId: userId,
    });

    if (!deletedLink) {
      throw new NotFoundError("Shareable link not found or you don't have permission to delete it.");
    }

    return deletedLink;
  } catch (error) {
    if (error instanceof BadRequestError || error instanceof NotFoundError) {
      throw error;
    }
    throw new BadRequestError("Failed to delete shareable link.");
  }
}

/**
 * Get shareable link analytics/stats
 */
export async function GetShareableLinkStatsService(linkId: string, userId: string) {
  try {
    if (!Types.ObjectId.isValid(linkId)) {
      throw new BadRequestError("Invalid link ID format.");
    }

    const link = await Link.findOne({
      _id: linkId,
      userId: userId,
    }).populate('contentId', 'title type');

    if (!link) {
      throw new NotFoundError("Shareable link not found or you don't have permission to view its stats.");
    }

    const stats = {
      linkId: link._id,
      shortCode: link.shortCode,
      content: link.contentId,
      currentAccesses: link.currentAccesses,
      maxAccesses: link.maxAccesses,
      expiresAt: link.expiresAt,
      isActive: link.isActive,
      createdAt: link.createdAt,
      updatedAt: link.updatedAt,
      accessesRemaining: link.maxAccesses ? link.maxAccesses - link.currentAccesses : null,
      isExpired: link.expiresAt ? new Date() > link.expiresAt : false,
      isMaxedOut: link.maxAccesses ? link.currentAccesses >= link.maxAccesses : false,
    };

    return stats;
  } catch (error) {
    if (error instanceof BadRequestError || error instanceof NotFoundError) {
      throw error;
    }
    throw new BadRequestError("Failed to get shareable link stats.");
  }
}
