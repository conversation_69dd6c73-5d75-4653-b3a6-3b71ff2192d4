import { serve } from "bun";
import { connectDB } from "./config/db";
import { PORT } from "./config/env";
import {
  UserLogin<PERSON>ontroller,
  UserLogoutController,
  UserRefreshTokenController,
  UserSignupController,
} from "./controllers/user.controller";
import { AppError } from "./utils/error";
import { ZodError } from "zod/v4";
import {
  apiRateLimitMiddleware,
  authMiddleware,
  authRateLimitMiddleware,
  corsMiddleware,
  detailedLoggingMiddleware,
  errorBoundaryMiddleware,
  publicRateLimitMiddleware,
  requestValidationMiddleware,
  securityHeadersMiddleware,
  simpleLoggingMiddleware,
  withMiddleware,
} from "./middleware/index.middleware";
import {
  BulkDeleteContentController,
  ContentCreateController,
  DeleteContentController,
  GetContentByIdController,
  GetContentStatsController,
  GetUserContentController,
  UpdateUserContentController,
} from "./controllers/content.controller";
import {
  AccessSharedContentController,
  CreateShareableLinkController,
  DeleteShareableLinkController,
  GetShareableLinkStatsController,
  GetUserShareableLinksController,
  UpdateShareableLinkController,
} from "./controllers/link.controller";
import {
  CreateTagController,
  DeleteTagController,
  GetAllTagsController,
  GetTagByIdController,
  SearchTagsController,
  UpdateTagController,
} from "./controllers/tags.controller";

connectDB();

// Configure CORS based on environment
const corsConfig = corsMiddleware({
  origin:
    process.env.NODE_ENV === "production"
      ? ["https://yourdomain.com", "https://www.yourdomain.com"]
      : true, // Allow all origins in development
  credentials: true,
  methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
});

const loggingConfig =
  process.env.NODE_ENV === "development"
    ? detailedLoggingMiddleware
    : simpleLoggingMiddleware;

const server = serve({
  port: PORT,
  // `routes` requires Bun v1.2.3+
  routes: {
    "/": withMiddleware(
      async () =>
        Response.json(
          {
            message:
              "Server is running fine, but this not the correct route to hit.",
          },
          { status: 200 }
        ),
      corsConfig,
      loggingConfig,
      securityHeadersMiddleware,
      publicRateLimitMiddleware
    ),
    "/api/status": new Response("OK"),
    // Static routes
    "/api/v1/login": {
      POST: withMiddleware(
        UserLoginController,
        loggingConfig,
        errorBoundaryMiddleware,
        corsConfig,
        requestValidationMiddleware,
        securityHeadersMiddleware,
        authRateLimitMiddleware
      ),
    },
    "/api/v1/signup": {
      POST: withMiddleware(
        UserSignupController,
        loggingConfig,
        errorBoundaryMiddleware,
        corsConfig,
        requestValidationMiddleware,
        securityHeadersMiddleware,
        authRateLimitMiddleware
      ),
    },
    "/api/v1/logout": {
      POST: withMiddleware(
        UserLogoutController,
        loggingConfig,
        errorBoundaryMiddleware,
        corsConfig,
        requestValidationMiddleware,
        securityHeadersMiddleware,
        authRateLimitMiddleware
      ),
    },
    "/api/v1/refresh": {
      POST: withMiddleware(
        UserRefreshTokenController,
        loggingConfig,
        errorBoundaryMiddleware,
        corsConfig,
        requestValidationMiddleware,
        securityHeadersMiddleware,
        authRateLimitMiddleware
      ),
    },
    "/api/v1/user/content": {
      POST: withMiddleware(
        ContentCreateController,
        loggingConfig,
        errorBoundaryMiddleware,
        corsConfig,
        requestValidationMiddleware,
        securityHeadersMiddleware,
        apiRateLimitMiddleware,
        authMiddleware
      ),
      GET: withMiddleware(
        GetUserContentController,
        loggingConfig,
        errorBoundaryMiddleware,
        corsConfig,
        requestValidationMiddleware,
        securityHeadersMiddleware,
        apiRateLimitMiddleware,
        authMiddleware
      ),
    },
    "/api/v1/user/content:id": {
      PUT: withMiddleware(
        UpdateUserContentController,
        loggingConfig,
        errorBoundaryMiddleware,
        corsConfig,
        requestValidationMiddleware,
        securityHeadersMiddleware,
        apiRateLimitMiddleware,
        authMiddleware
      ),
      GET: withMiddleware(
        GetContentByIdController,
        loggingConfig,
        errorBoundaryMiddleware,
        corsConfig,
        requestValidationMiddleware,
        securityHeadersMiddleware,
        apiRateLimitMiddleware,
        authMiddleware
      ),
      DELETE: withMiddleware(
        DeleteContentController,
        loggingConfig,
        errorBoundaryMiddleware,
        corsConfig,
        requestValidationMiddleware,
        securityHeadersMiddleware,
        apiRateLimitMiddleware,
        authMiddleware
      ),
    },
    "/api/v1/user/content/stats": {
      GET: withMiddleware(
        GetContentStatsController,
        loggingConfig,
        errorBoundaryMiddleware,
        corsConfig,
        securityHeadersMiddleware,
        apiRateLimitMiddleware,
        authMiddleware
      ),
    },
    "/api/v1/user/content/bulk": {
      DELETE: withMiddleware(
        BulkDeleteContentController,
        loggingConfig,
        errorBoundaryMiddleware,
        corsConfig,
        requestValidationMiddleware,
        securityHeadersMiddleware,
        apiRateLimitMiddleware,
        authMiddleware
      ),
    },

    // Tags routes
    "/api/v1/tags": {
      GET: withMiddleware(
        GetAllTagsController,
        loggingConfig,
        errorBoundaryMiddleware,
        corsConfig,
        securityHeadersMiddleware,
        apiRateLimitMiddleware,
        authMiddleware
      ),
      POST: withMiddleware(
        CreateTagController,
        loggingConfig,
        errorBoundaryMiddleware,
        corsConfig,
        requestValidationMiddleware,
        securityHeadersMiddleware,
        apiRateLimitMiddleware,
        authMiddleware
      ),
    },
    "/api/v1/tags/search": {
      GET: withMiddleware(
        SearchTagsController,
        loggingConfig,
        errorBoundaryMiddleware,
        corsConfig,
        securityHeadersMiddleware,
        apiRateLimitMiddleware,
        authMiddleware
      ),
    },
    "/api/v1/tags:id": {
      GET: withMiddleware(
        GetTagByIdController,
        loggingConfig,
        errorBoundaryMiddleware,
        corsConfig,
        securityHeadersMiddleware,
        apiRateLimitMiddleware,
        authMiddleware
      ),
    },

    // Shareable Links routes
    "/api/v1/links": {
      GET: withMiddleware(
        GetUserShareableLinksController,
        loggingConfig,
        errorBoundaryMiddleware,
        corsConfig,
        securityHeadersMiddleware,
        apiRateLimitMiddleware,
        authMiddleware
      ),
      POST: withMiddleware(
        CreateShareableLinkController,
        loggingConfig,
        errorBoundaryMiddleware,
        corsConfig,
        requestValidationMiddleware,
        securityHeadersMiddleware,
        apiRateLimitMiddleware,
        authMiddleware
      ),
    },

    // Public shareable link access (no auth required)
    "/share/*": {
      GET: withMiddleware(
        AccessSharedContentController,
        loggingConfig,
        errorBoundaryMiddleware,
        corsConfig,
        securityHeadersMiddleware,
        publicRateLimitMiddleware
      ),
    },

    // Dynamic routes
    // "/users/:id": (req) => {
    //   return new Response(`Hello User ${req.params.id}!`);
    // },

    // Per-HTTP method handlers
    // "/api/posts": {
    //   GET: () => new Response("List posts"),
    //   POST: async (req) => {
    //     const body = await req.json();
    //     return Response.json({ created: true, ...body });
    //   },
    // },

    // Wildcard route for all routes that start with "/api/" and aren't otherwise matched
    "/api/*": Response.json({ message: "Not found" }, { status: 404 }),

    // Redirect from /blog/hello to /blog/hello/world
    "/blog/hello": Response.redirect("/blog/hello/world"),

    // Serve a file by buffering it in memory
    // "/favicon.ico": new Response(await Bun.file("./favicon.ico").bytes(), {
    //   headers: {
    //     "Content-Type": "image/x-icon",
    //   },
    // }),
  },

  // (optional) fallback for unmatched routes:
  // Required if Bun's version < 1.2.3
  async fetch(req) {
    const url = new URL(req.url);
    const path = url.pathname;
    const method = req.method;

    // Handle dynamic tag routes: /api/v1/tags/:id
    const tagIdMatch = path.match(/^\/api\/v1\/tags\/([a-fA-F0-9]{24})$/);
    if (tagIdMatch) {
      const middleware = withMiddleware(
        method === "GET"
          ? GetTagByIdController
          : method === "PUT"
          ? UpdateTagController
          : method === "DELETE"
          ? DeleteTagController
          : async () =>
              Response.json(
                { success: false, message: "Method not allowed" },
                { status: 405 }
              ),
        loggingConfig,
        errorBoundaryMiddleware,
        corsConfig,
        ...(method !== "GET" ? [requestValidationMiddleware] : []),
        securityHeadersMiddleware,
        apiRateLimitMiddleware,
        authMiddleware
      );
      return await middleware(req as any);
    }

    // Handle dynamic content routes: /api/v1/user/content/:id
    const contentIdMatch = path.match(
      /^\/api\/v1\/user\/content\/([a-fA-F0-9]{24})$/
    );
    if (contentIdMatch) {
      const middleware = withMiddleware(
        method === "GET"
          ? GetContentByIdController
          : method === "DELETE"
          ? DeleteContentController
          : async () =>
              Response.json(
                { success: false, message: "Method not allowed" },
                { status: 405 }
              ),
        loggingConfig,
        errorBoundaryMiddleware,
        corsConfig,
        securityHeadersMiddleware,
        apiRateLimitMiddleware,
        authMiddleware
      );
      return await middleware(req as any);
    }

    // Handle dynamic link routes: /api/v1/links/:id
    const linkIdMatch = path.match(/^\/api\/v1\/links\/([a-fA-F0-9]{24})$/);
    if (linkIdMatch) {
      const middleware = withMiddleware(
        method === "PUT"
          ? UpdateShareableLinkController
          : method === "DELETE"
          ? DeleteShareableLinkController
          : async () =>
              Response.json(
                { success: false, message: "Method not allowed" },
                { status: 405 }
              ),
        loggingConfig,
        errorBoundaryMiddleware,
        corsConfig,
        requestValidationMiddleware,
        securityHeadersMiddleware,
        apiRateLimitMiddleware,
        authMiddleware
      );
      return await middleware(req as any);
    }

    // Handle link stats routes: /api/v1/links/:id/stats
    const linkStatsMatch = path.match(
      /^\/api\/v1\/links\/([a-fA-F0-9]{24})\/stats$/
    );
    if (linkStatsMatch) {
      const middleware = withMiddleware(
        GetShareableLinkStatsController,
        loggingConfig,
        errorBoundaryMiddleware,
        corsConfig,
        securityHeadersMiddleware,
        apiRateLimitMiddleware,
        authMiddleware
      );
      return await middleware(req as any);
    }

    // Handle public share routes: /share/:shortCode
    const shareMatch = path.match(/^\/share\/([a-zA-Z0-9_-]+)$/);
    if (shareMatch) {
      const middleware = withMiddleware(
        AccessSharedContentController,
        loggingConfig,
        errorBoundaryMiddleware,
        corsConfig,
        securityHeadersMiddleware,
        publicRateLimitMiddleware
      );
      return await middleware(req as any);
    }

    // Handle CORS preflight for all API routes
    if (method === "OPTIONS" && path.startsWith("/api/")) {
      return new Response(null, {
        status: 204,
        headers: {
          "Access-Control-Allow-Origin":
            process.env.NODE_ENV === "production"
              ? "https://yourdomain.com"
              : "*",
          "Access-Control-Allow-Methods":
            "GET, POST, PUT, DELETE, PATCH, OPTIONS",
          "Access-Control-Allow-Headers":
            "Content-Type, Authorization, X-Requested-With",
          "Access-Control-Allow-Credentials": "true",
        },
      });
    }

    return new Response("Not Found", { status: 404 });
  },

  error(err: Error): Response {
    console.error("Caught by Bun.server global error handler:", err);

    let statusCode = 500;
    let message = "Internal Server Error";
    let errors: any[] | undefined = undefined;

    if (err instanceof AppError) {
      statusCode = err.statusCode;
      message = err.message;

      if (!err.isOperational) {
        console.error("Non-operational error (likely a bug):", err.stack);
      }
    } else if ((err as any).code === 11000) {
      statusCode = 409;
      message = "User already exists.";
      if ((err as any).keyValue) {
        message += `(Key: ${Object.keys((err as any).keyValue).join(",")})`;
      }
    } else if (err instanceof ZodError) {
      statusCode = 400;
      message = "Validation failed";
      errors = err.issues.map((e) => ({
        path: e.path.join("."),
        message: e.message,
      }));
    } else {
      if (process.env.NODE_ENV === "production") {
        message = "An unexpected server error occured.";
      } else {
        message = `An unexpected server error occured: ${err.message}`;
      }
    }

    return Response.json(
      {
        success: false,
        message: message,
        errors: errors,
      },
      {
        status: statusCode,
        headers: { "Content-Type": "application/json" },
      }
    );
  },
});

console.log(`🚀 Server running at ${server.url}`);
