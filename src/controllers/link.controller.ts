import { ZodError } from "zod/v4";
import type { AuthenticatedRequest } from "../middleware/auth.middleware";
import { LinkSchema, type LinkInput } from "../schema/link.schema";
import {
  CreateShareableLinkService,
  DeleteShareableLinkService,
  GetShareableLinkByCodeService,
  GetShareableLinkStatsService,
  GetUserShareableLinksService,
  UpdateShareableLinkService,
} from "../services/link.service";
import { AppError } from "../utils/error";
import type { BunRequest } from "bun";

/**
 * Create a shareable link for content
 * POST /api/v1/links
 */
export async function CreateShareableLinkController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const body = await req.json();
    const userId = req.user!.id;

    const validLinkData: LinkInput = LinkSchema.parse(body);

    const shareableLink = await CreateShareableLinkService(userId, validLinkData);

    return Response.json(
      {
        success: true,
        message: "Shareable link created successfully.",
        data: {
          ...shareableLink,
          shareUrl: `${process.env.APP_BASE_URL || 'http://localhost:3000'}/share/${shareableLink!.shortCode}`,
        },
      },
      {
        status: 201,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if (error instanceof ZodError) {
      return Response.json(
        {
          success: false,
          message: "Validation failed",
          errors: error.issues.map((issue) => ({
            path: issue.path.join("."),
            message: issue.message,
          })),
        },
        { status: 400 }
      );
    }

    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    console.error("Create shareable link error:", error);
    return Response.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * Get all shareable links for authenticated user
 * GET /api/v1/links?page=1&limit=20&active=true
 */
export async function GetUserShareableLinksController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "20");
    const activeParam = url.searchParams.get("active");
    const isActive = activeParam ? activeParam === "true" : undefined;

    const userId = req.user!.id;

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return Response.json(
        {
          success: false,
          message: "Invalid pagination parameters. Page must be >= 1, limit must be 1-100.",
        },
        { status: 400 }
      );
    }

    const result = await GetUserShareableLinksService(userId, page, limit, isActive);

    // Add share URLs to each link
    const linksWithUrls = result.links.map(link => ({
      ...link,
      shareUrl: `${process.env.APP_BASE_URL || 'http://localhost:3000'}/share/${link.shortCode}`,
    }));

    return Response.json(
      {
        success: true,
        message: "Shareable links retrieved successfully.",
        data: linksWithUrls,
        pagination: result.pagination,
      },
      {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    console.error("Get user shareable links error:", error);
    return Response.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * Access shared content via short code (public endpoint)
 * GET /share/:shortCode
 */
export async function AccessSharedContentController(
  req: BunRequest
): Promise<Response> {
  try {
    const url = new URL(req.url);
    const pathParts = url.pathname.split("/");
    const shortCode = pathParts[pathParts.length - 1];

    if (!shortCode) {
      return Response.json(
        {
          success: false,
          message: "Short code is required.",
        },
        { status: 400 }
      );
    }

    const sharedContent = await GetShareableLinkByCodeService(shortCode);

    return Response.json(
      {
        success: true,
        message: "Shared content accessed successfully.",
        data: sharedContent,
      },
      {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    console.error("Access shared content error:", error);
    return Response.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * Update shareable link settings
 * PUT /api/v1/links/:id
 */
export async function UpdateShareableLinkController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const url = new URL(req.url);
    const pathParts = url.pathname.split("/");
    const linkId = pathParts[pathParts.length - 1];

    if (!linkId) {
      return Response.json(
        {
          success: false,
          message: "Link ID is required.",
        },
        { status: 400 }
      );
    }

    const body = await req.json();
    const userId = req.user!.id;

    // Validate only the fields that can be updated
    const updateSchema = LinkSchema.partial().pick({
      expiresAt: true,
      maxAccesses: true,
      isActive: true,
    });

    const validUpdateData = updateSchema.parse(body);

    const updatedLink = await UpdateShareableLinkService(linkId, userId, validUpdateData);

    return Response.json(
      {
        success: true,
        message: "Shareable link updated successfully.",
        data: {
          ...updatedLink?.toObject(),
          shareUrl: `${process.env.APP_BASE_URL || 'http://localhost:3000'}/share/${updatedLink?.shortCode}`,
        },
      },
      {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if (error instanceof ZodError) {
      return Response.json(
        {
          success: false,
          message: "Validation failed",
          errors: error.issues.map((issue) => ({
            path: issue.path.join("."),
            message: issue.message,
          })),
        },
        { status: 400 }
      );
    }

    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    console.error("Update shareable link error:", error);
    return Response.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * Delete shareable link
 * DELETE /api/v1/links/:id
 */
export async function DeleteShareableLinkController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const url = new URL(req.url);
    const pathParts = url.pathname.split("/");
    const linkId = pathParts[pathParts.length - 1];

    if (!linkId) {
      return Response.json(
        {
          success: false,
          message: "Link ID is required.",
        },
        { status: 400 }
      );
    }

    const userId = req.user!.id;
    const deletedLink = await DeleteShareableLinkService(linkId, userId);

    return Response.json(
      {
        success: true,
        message: "Shareable link deleted successfully.",
        data: deletedLink,
      },
      {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    console.error("Delete shareable link error:", error);
    return Response.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * Get shareable link statistics
 * GET /api/v1/links/:id/stats
 */
export async function GetShareableLinkStatsController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const url = new URL(req.url);
    const pathParts = url.pathname.split("/");
    const linkId = pathParts[pathParts.length - 2]; // Because URL ends with /stats

    if (!linkId) {
      return Response.json(
        {
          success: false,
          message: "Link ID is required.",
        },
        { status: 400 }
      );
    }

    const userId = req.user!.id;
    const stats = await GetShareableLinkStatsService(linkId, userId);

    return Response.json(
      {
        success: true,
        message: "Shareable link stats retrieved successfully.",
        data: {
          ...stats,
          shareUrl: `${process.env.APP_BASE_URL || 'http://localhost:3000'}/share/${stats.shortCode}`,
        },
      },
      {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    console.error("Get shareable link stats error:", error);
    return Response.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}
