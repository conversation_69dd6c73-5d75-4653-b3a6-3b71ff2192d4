import { Zod<PERSON>rror } from "zod/v4";
import type { AuthenticatedRequest } from "../middleware/auth.middleware";
import { TagsSchema, type TagsTypes } from "../schema/tags.schema";
import {
  CreateTagService,
  DeleteTagService,
  GetAllTagsService,
  GetTagByIdService,
  SearchTagsService,
  UpdateTagService,
} from "../services/tags.service";
import { AppError } from "../utils/error";

/**
 * Create a new tag
 * POST /api/v1/tags
 */
export async function CreateTagController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const body = await req.json();
    
    const validTagData: TagsTypes = TagsSchema.parse(body);
    
    const newTag = await CreateTagService(validTagData);

    return Response.json(
      {
        success: true,
        message: "Tag created successfully.",
        data: newTag,
      },
      {
        status: 201,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if (error instanceof ZodError) {
      return Response.json(
        {
          success: false,
          message: "Validation failed",
          errors: error.issues.map((issue) => ({
            path: issue.path.join("."),
            message: issue.message,
          })),
        },
        { status: 400 }
      );
    }
    
    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    console.error("Create tag error:", error);
    return Response.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * Get all tags with pagination and search
 * GET /api/v1/tags?page=1&limit=20&search=term
 */
export async function GetAllTagsController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "20");
    const search = url.searchParams.get("search") || undefined;

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return Response.json(
        {
          success: false,
          message: "Invalid pagination parameters. Page must be >= 1, limit must be 1-100.",
        },
        { status: 400 }
      );
    }

    const result = await GetAllTagsService(page, limit, search);

    return Response.json(
      {
        success: true,
        message: "Tags retrieved successfully.",
        data: result.tags,
        pagination: result.pagination,
      },
      {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    console.error("Get all tags error:", error);
    return Response.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * Get a single tag by ID
 * GET /api/v1/tags/:id
 */
export async function GetTagByIdController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const url = new URL(req.url);
    const pathParts = url.pathname.split("/");
    const tagId = pathParts[pathParts.length - 1];

    if (!tagId) {
      return Response.json(
        {
          success: false,
          message: "Tag ID is required.",
        },
        { status: 400 }
      );
    }

    const tag = await GetTagByIdService(tagId);

    return Response.json(
      {
        success: true,
        message: "Tag retrieved successfully.",
        data: tag,
      },
      {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    console.error("Get tag by ID error:", error);
    return Response.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * Update a tag by ID
 * PUT /api/v1/tags/:id
 */
export async function UpdateTagController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const url = new URL(req.url);
    const pathParts = url.pathname.split("/");
    const tagId = pathParts[pathParts.length - 1];

    if (!tagId) {
      return Response.json(
        {
          success: false,
          message: "Tag ID is required.",
        },
        { status: 400 }
      );
    }

    const body = await req.json();
    const validTagData: TagsTypes = TagsSchema.parse(body);

    const updatedTag = await UpdateTagService(tagId, validTagData);

    return Response.json(
      {
        success: true,
        message: "Tag updated successfully.",
        data: updatedTag,
      },
      {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if (error instanceof ZodError) {
      return Response.json(
        {
          success: false,
          message: "Validation failed",
          errors: error.issues.map((issue) => ({
            path: issue.path.join("."),
            message: issue.message,
          })),
        },
        { status: 400 }
      );
    }

    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    console.error("Update tag error:", error);
    return Response.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * Delete a tag by ID
 * DELETE /api/v1/tags/:id
 */
export async function DeleteTagController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const url = new URL(req.url);
    const pathParts = url.pathname.split("/");
    const tagId = pathParts[pathParts.length - 1];

    if (!tagId) {
      return Response.json(
        {
          success: false,
          message: "Tag ID is required.",
        },
        { status: 400 }
      );
    }

    const deletedTag = await DeleteTagService(tagId);

    return Response.json(
      {
        success: true,
        message: "Tag deleted successfully.",
        data: deletedTag,
      },
      {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    console.error("Delete tag error:", error);
    return Response.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * Search tags (for autocomplete/suggestions)
 * GET /api/v1/tags/search?q=searchterm&limit=10
 */
export async function SearchTagsController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const url = new URL(req.url);
    const searchTerm = url.searchParams.get("q") || "";
    const limit = parseInt(url.searchParams.get("limit") || "10");

    if (limit < 1 || limit > 50) {
      return Response.json(
        {
          success: false,
          message: "Limit must be between 1 and 50.",
        },
        { status: 400 }
      );
    }

    const tags = await SearchTagsService(searchTerm, limit);

    return Response.json(
      {
        success: true,
        message: "Tags search completed.",
        data: tags,
      },
      {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    console.error("Search tags error:", error);
    return Response.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}
