import {
  ContentIdSchema,
  ContentSchema,
  type ContentIdType,
  type ContentTypes,
} from "../schema/content.schema";
import { ZodError } from "zod/v4";
import type { AuthenticatedRequest } from "../middleware/auth.middleware";
import {
  CreateContentService,
  GetContentService,
  GetUserContentService,
  UpdateUserContentService,
} from "../services/content.service";
import { AppError } from "../utils/error";

export async function ContentCreateController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const body = await req.json();
    const id = req.user?.id;

    const validContentData: ContentTypes = ContentSchema.parse(body);

    const contentData = await CreateContentService(id, {
      ...validContentData,
      userId: id,
    });

    return Response.json(
      { success: true, message: "Link saved successfull.", contentData },
      {
        status: 200,
        statusText: "Link saved.",
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0,",
        },
      }
    );
  } catch (error) {
    if (error instanceof ZodError) {
      return Response.json(
        {
          success: false,
          message: "Validation failed",
          errors: error.issues.map((issue) => ({
            path: issue.path.join("."),
            message: issue.message,
          })),
        },
        { status: 400 }
      );
    }
    throw error;
  }
}

export async function GetUserContentController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const id = req.user?.id;

    const userContentData = await GetUserContentService(id);

    return Response.json(
      {
        success: true,
        message: "User Content fetch successfull.",
        userContentData,
      },
      {
        status: 200,
        statusText: "Content fetch successfull.",
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0,",
        },
      }
    );
  } catch (error) {
    throw error;
  }
}

export async function UpdateUserContentController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const urlParts = new URL(req.url).pathname.split("/");
    const contId = urlParts[urlParts.length - 1];

    const contentId: ContentIdType = ContentIdSchema.parse(contId);
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError("Unauthorized: User ID not found in request.", 401);
    }

    const body = await req.json();

    const validContentData: ContentTypes = ContentSchema.parse(body);

    const updatedData = await UpdateUserContentService(
      contentId,
      userId,
      validContentData
    );

    return Response.json(
      {
        success: true,
        message: "Content update successfull.",
        updatedData,
      },
      {
        status: 200,
        statusText: "Content update successfull.",
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0,",
        },
      }
    );
  } catch (error) {
    if (error instanceof ZodError) {
      return Response.json(
        {
          success: false,
          message: "Validation failed",
          errors: error.issues.map((issue) => ({
            path: issue.path.join("."),
            message: issue.message,
          })),
        },
        { status: 400 }
      );
    }
    throw error;
  }
}

export async function GetContentController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const urlParts = new URL(req.url).pathname.split("/");
    const contId = urlParts[urlParts.length - 1];

    const contentId: ContentIdType = ContentIdSchema.parse(contId);

    const contentData = await GetContentService(contentId);

    return Response.json(
      {
        success: true,
        message: "Content fetch successfull.",
        contentData,
      },
      {
        status: 200,
        statusText: "Content fetch successfull.",
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0,",
        },
      }
    );
  } catch (error) {
    if (error instanceof ZodError) {
      return Response.json(
        {
          success: false,
          message: "Validation failed",
          errors: error.issues.map((issue) => ({
            path: issue.path.join("."),
            message: issue.message,
          })),
        },
        { status: 400 }
      );
    }
    throw error;
  }
}
