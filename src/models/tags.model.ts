import { Document, model, Schema } from "mongoose";

export interface ITags extends Document {
  title: string;
  createdAt: Date;
  updatedAt: Date;
}

const TagsSchema = new Schema(
  {
    title: {
      type: String,
      required: [true, "Please provide a tag for this content"],
      unique: [true, "Tag already exist"],
      lowercase: true,
      trim: true,
    },
  },
  { timestamps: true },
);

export const Tags = model<ITags>("Tag", TagsSchema);
