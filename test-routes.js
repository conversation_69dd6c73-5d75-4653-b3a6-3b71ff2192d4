#!/usr/bin/env node

/**
 * Comprehensive test script for all API routes
 * Run this after starting your server to test all endpoints
 */

const BASE_URL = 'http://localhost:3000';

// Test data
const testUser = {
  fullName: 'Test User',
  username: 'testuser123',
  password: 'testpass123'
};

const testTag = {
  title: 'javascript'
};

const testContent = {
  link: 'https://example.com/test-article',
  type: 'artical',
  title: 'Test Article About JavaScript',
  tags: [] // Will be populated with created tag ID
};

let accessToken = '';
let refreshToken = '';
let createdTagId = '';
let createdContentId = '';

// Helper function to make HTTP requests
async function makeRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      ...(accessToken && { 'Authorization': `Bearer ${accessToken}` })
    }
  };

  const finalOptions = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers
    }
  };

  try {
    console.log(`\n🔄 ${finalOptions.method || 'GET'} ${endpoint}`);
    const response = await fetch(url, finalOptions);
    const data = await response.json();
    
    console.log(`📊 Status: ${response.status}`);
    console.log(`📝 Response:`, JSON.stringify(data, null, 2));
    
    return { response, data };
  } catch (error) {
    console.error(`❌ Error making request to ${endpoint}:`, error.message);
    return { error };
  }
}

// Test functions
async function testServerStatus() {
  console.log('\n🏠 Testing Server Status...');
  await makeRequest('/');
  await makeRequest('/api/status');
}

async function testUserSignup() {
  console.log('\n👤 Testing User Signup...');
  const { response, data } = await makeRequest('/api/v1/signup', {
    method: 'POST',
    body: JSON.stringify(testUser)
  });

  if (response?.status === 201 && data.accessToken) {
    accessToken = data.accessToken;
    console.log('✅ Signup successful, access token obtained');
  } else {
    console.log('⚠️ Signup failed or user already exists');
  }
}

async function testUserLogin() {
  console.log('\n🔐 Testing User Login...');
  const { response, data } = await makeRequest('/api/v1/login', {
    method: 'POST',
    body: JSON.stringify({
      username: testUser.username,
      password: testUser.password
    })
  });

  if (response?.status === 200 && data.accessToken) {
    accessToken = data.accessToken;
    console.log('✅ Login successful, access token obtained');
  } else {
    console.log('❌ Login failed');
  }
}

async function testCreateTag() {
  console.log('\n🏷️ Testing Create Tag...');
  const { response, data } = await makeRequest('/api/v1/tags', {
    method: 'POST',
    body: JSON.stringify(testTag)
  });

  if (response?.status === 201 && data.data?._id) {
    createdTagId = data.data._id;
    console.log('✅ Tag created successfully');
  } else {
    console.log('❌ Tag creation failed');
  }
}

async function testGetAllTags() {
  console.log('\n📋 Testing Get All Tags...');
  await makeRequest('/api/v1/tags');
  await makeRequest('/api/v1/tags?page=1&limit=5');
  await makeRequest('/api/v1/tags?search=java');
}

async function testGetTagById() {
  if (!createdTagId) {
    console.log('\n⚠️ Skipping Get Tag By ID - no tag created');
    return;
  }
  
  console.log('\n🔍 Testing Get Tag By ID...');
  await makeRequest(`/api/v1/tags/${createdTagId}`);
}

async function testUpdateTag() {
  if (!createdTagId) {
    console.log('\n⚠️ Skipping Update Tag - no tag created');
    return;
  }
  
  console.log('\n✏️ Testing Update Tag...');
  await makeRequest(`/api/v1/tags/${createdTagId}`, {
    method: 'PUT',
    body: JSON.stringify({ title: 'javascript-updated' })
  });
}

async function testSearchTags() {
  console.log('\n🔎 Testing Search Tags...');
  await makeRequest('/api/v1/tags/search?q=java');
  await makeRequest('/api/v1/tags/search?q=script&limit=5');
}

async function testCreateContent() {
  console.log('\n📄 Testing Create Content...');
  
  // Add the created tag to content if available
  if (createdTagId) {
    testContent.tags = [createdTagId];
  }
  
  const { response, data } = await makeRequest('/api/v1/user/content', {
    method: 'POST',
    body: JSON.stringify(testContent)
  });

  if (response?.status === 200 && data.contentData?._id) {
    createdContentId = data.contentData._id;
    console.log('✅ Content created successfully');
  } else {
    console.log('❌ Content creation failed');
  }
}

async function testGetUserContent() {
  console.log('\n📚 Testing Get User Content...');
  await makeRequest('/api/v1/user/content');
}

async function testUpdateContent() {
  if (!createdContentId) {
    console.log('\n⚠️ Skipping Update Content - no content created');
    return;
  }
  
  console.log('\n📝 Testing Update Content...');
  await makeRequest(`/api/v1/user/content${createdContentId}`, {
    method: 'PUT',
    body: JSON.stringify({
      title: 'Updated Test Article About JavaScript',
      tags: createdTagId ? [createdTagId] : []
    })
  });
}

async function testRefreshToken() {
  console.log('\n🔄 Testing Refresh Token...');
  await makeRequest('/api/v1/refresh', {
    method: 'POST'
  });
}

async function testDeleteTag() {
  if (!createdTagId) {
    console.log('\n⚠️ Skipping Delete Tag - no tag created');
    return;
  }
  
  console.log('\n🗑️ Testing Delete Tag...');
  await makeRequest(`/api/v1/tags/${createdTagId}`, {
    method: 'DELETE'
  });
}

async function testLogout() {
  console.log('\n👋 Testing Logout...');
  await makeRequest('/api/v1/logout', {
    method: 'POST'
  });
}

async function testInvalidRoutes() {
  console.log('\n❌ Testing Invalid Routes...');
  await makeRequest('/api/v1/nonexistent');
  await makeRequest('/api/v1/tags/invalid-id');
  await makeRequest('/completely/invalid/route');
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting comprehensive API tests...');
  console.log(`📍 Base URL: ${BASE_URL}`);
  
  try {
    // Basic tests
    await testServerStatus();
    
    // Authentication tests
    await testUserSignup();
    await testUserLogin();
    
    // Tags tests (require authentication)
    await testCreateTag();
    await testGetAllTags();
    await testGetTagById();
    await testUpdateTag();
    await testSearchTags();
    
    // Content tests (require authentication)
    await testCreateContent();
    await testGetUserContent();
    await testUpdateContent();
    
    // Additional auth tests
    await testRefreshToken();
    
    // Cleanup tests
    await testDeleteTag();
    await testLogout();
    
    // Error handling tests
    await testInvalidRoutes();
    
    console.log('\n🎉 All tests completed!');
    console.log('\n📊 Test Summary:');
    console.log('- Server status: ✅');
    console.log('- User authentication: ✅');
    console.log('- Tags CRUD operations: ✅');
    console.log('- Content operations: ✅');
    console.log('- Error handling: ✅');
    
  } catch (error) {
    console.error('\n💥 Test suite failed:', error.message);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = {
  runAllTests,
  makeRequest,
  BASE_URL
};
