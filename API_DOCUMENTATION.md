# 📚 Second Brain API Documentation

Complete API documentation for the Second Brain application with tags functionality.

## 🔗 Base URL
```
http://localhost:3000
```

## 🔐 Authentication

All protected routes require an access token in the Authorization header:
```
Authorization: Bearer <access_token>
```

## 📋 API Endpoints

### 🏠 Server Status

#### Get Server Status
```http
GET /
GET /api/status
```

**Response:**
```json
{
  "message": "Server is running fine, but this not the correct route to hit."
}
```

---

### 👤 User Authentication

#### User Signup
```http
POST /api/v1/signup
```

**Request Body:**
```json
{
  "fullName": "<PERSON>",
  "username": "johndoe123",
  "password": "password123"
}
```

**Response:**
```json
{
  "message": "Signup successfull.",
  "user": {
    "id": "user_id",
    "fullName": "John Doe",
    "username": "johndoe123"
  },
  "accessToken": "jwt_access_token"
}
```

#### User Login
```http
POST /api/v1/login
```

**Request Body:**
```json
{
  "username": "johndoe123",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successfull.",
  "user": {
    "id": "user_id",
    "fullName": "John Doe",
    "username": "johndoe123"
  },
  "accessToken": "jwt_access_token"
}
```

#### Refresh Token
```http
POST /api/v1/refresh
```

**Response:**
```json
{
  "success": true,
  "message": "Token refreshed.",
  "accessToken": "new_jwt_access_token"
}
```

#### User Logout
```http
POST /api/v1/logout
```

**Response:**
```json
{
  "success": true,
  "message": "Logout successful."
}
```

---

### 🏷️ Tags Management

#### Create Tag
```http
POST /api/v1/tags
Authorization: Bearer <access_token>
```

**Request Body:**
```json
{
  "title": "javascript"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Tag created successfully.",
  "data": {
    "_id": "tag_id",
    "title": "javascript",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

#### Get All Tags
```http
GET /api/v1/tags?page=1&limit=20&search=term
Authorization: Bearer <access_token>
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20, max: 100)
- `search` (optional): Search term for tag titles

**Response:**
```json
{
  "success": true,
  "message": "Tags retrieved successfully.",
  "data": [
    {
      "_id": "tag_id",
      "title": "javascript",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 1,
    "totalCount": 1,
    "hasNextPage": false,
    "hasPrevPage": false,
    "limit": 20
  }
}
```

#### Get Tag by ID
```http
GET /api/v1/tags/:id
Authorization: Bearer <access_token>
```

**Response:**
```json
{
  "success": true,
  "message": "Tag retrieved successfully.",
  "data": {
    "_id": "tag_id",
    "title": "javascript",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

#### Update Tag
```http
PUT /api/v1/tags/:id
Authorization: Bearer <access_token>
```

**Request Body:**
```json
{
  "title": "updated-javascript"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Tag updated successfully.",
  "data": {
    "_id": "tag_id",
    "title": "updated-javascript",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

#### Delete Tag
```http
DELETE /api/v1/tags/:id
Authorization: Bearer <access_token>
```

**Response:**
```json
{
  "success": true,
  "message": "Tag deleted successfully.",
  "data": {
    "_id": "tag_id",
    "title": "javascript",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

#### Search Tags
```http
GET /api/v1/tags/search?q=searchterm&limit=10
Authorization: Bearer <access_token>
```

**Query Parameters:**
- `q` (required): Search term
- `limit` (optional): Maximum results (default: 10, max: 50)

**Response:**
```json
{
  "success": true,
  "message": "Tags search completed.",
  "data": [
    {
      "_id": "tag_id",
      "title": "javascript",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

---

### 📄 Content Management

#### Create Content
```http
POST /api/v1/user/content
Authorization: Bearer <access_token>
```

**Request Body:**
```json
{
  "link": "https://example.com/article",
  "type": "artical",
  "title": "Amazing JavaScript Tutorial",
  "tags": ["tag_id_1", "tag_id_2"]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Link saved successfull.",
  "contentData": {
    "_id": "content_id",
    "link": "https://example.com/article",
    "type": "artical",
    "title": "Amazing JavaScript Tutorial",
    "tags": ["tag_id_1", "tag_id_2"],
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

#### Get User Content
```http
GET /api/v1/user/content
Authorization: Bearer <access_token>
```

**Response:**
```json
{
  "success": true,
  "message": "Content fetch successfull.",
  "contentData": [
    {
      "_id": "content_id",
      "link": "https://example.com/article",
      "type": "artical",
      "title": "Amazing JavaScript Tutorial",
      "tags": ["tag_id_1", "tag_id_2"],
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

#### Update Content
```http
PUT /api/v1/user/content:id
Authorization: Bearer <access_token>
```

**Request Body:**
```json
{
  "title": "Updated JavaScript Tutorial",
  "tags": ["tag_id_1"]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Content updated successfully.",
  "data": {
    "_id": "content_id",
    "link": "https://example.com/article",
    "type": "artical",
    "title": "Updated JavaScript Tutorial",
    "tags": ["tag_id_1"],
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

---

## 🚨 Error Responses

### Validation Error (400)
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "path": "title",
      "message": "Tag must be of at least 3 characters."
    }
  ]
}
```

### Authentication Error (401)
```json
{
  "success": false,
  "message": "Access token required. Please log in."
}
```

### Not Found Error (404)
```json
{
  "success": false,
  "message": "Tag not found."
}
```

### Conflict Error (409)
```json
{
  "success": false,
  "message": "Tag 'javascript' already exists."
}
```

### Rate Limit Error (429)
```json
{
  "success": false,
  "message": "Too many requests, please try again later.",
  "retryAfter": 60
}
```

### Server Error (500)
```json
{
  "success": false,
  "message": "Internal server error"
}
```

---

## 🧪 Testing

Run the test script to verify all endpoints:

```bash
# Start your server first
bun run dev

# In another terminal, run tests
node test-routes.js
```

## 📝 Notes

1. **Authentication**: All routes except signup, login, and server status require authentication
2. **Rate Limiting**: Different endpoints have different rate limits (auth endpoints are more restrictive)
3. **CORS**: Configured for development (all origins) and production (specific domains)
4. **Validation**: All input data is validated using Zod schemas
5. **Error Handling**: Comprehensive error handling with proper HTTP status codes
