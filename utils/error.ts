export class AppError extends <PERSON>rror {
  statusCode: number;
  isOperational: boolean;

  constructor(message: string, statusCode: number = 500) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;
    //Capture stack trace for debugging purposes
    Error.captureStackTrace(this, this.constructor);
  }
}

export class BadRequestError extends AppError {
  constructor(message: string = "Bad Request", statusCode: number = 400) {
    super(message, statusCode);
  }
}

export class UnathorizedError extends AppError {
  constructor(message: string = "Unathorized", statusCode: number = 401) {
    super(message, statusCode);
  }
}

export class ForbiddenError extends AppError {
  constructor(message: string = "Forbidden", statusCode: number = 403) {
    super(message, statusCode);
  }
}

export class NotFoundError extends AppError {
  constructor(
    message: string = "Resource not found",
    statusCode: number = 404,
  ) {
    super(message, statusCode);
  }
}

export class ConflictError extends AppError {
  constructor(message: string = "Conflict", statusCode: number = 409) {
    super(message, statusCode);
  }
}

export class InternalServerError extends AppError {
  constructor(
    message: string = "Internal Server Error",
    statusCode: number = 500,
  ) {
    super(message, statusCode);
    this.isOperational = false;
  }
}
