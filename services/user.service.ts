import type {
  UserSignupInputType,
  UserLoginInputType,
} from "../schema/user.schema";
import { User } from "../models/user.model";
import { ConflictError, UnathorizedError } from "../utils/error";
import {
  generateAccessToken,
  generateRefreshToken,
  hashToken,
} from "../utils/generateToken";
import jwt from "jsonwebtoken";
import { JWT_REFRESH_SECRET } from "../config/env";

export async function UserLoginService({
  username,
  password,
}: UserLoginInputType) {
  const user = await User.findOne({ username }).select(
    "+password +refreshToken"
  );
  if (!user) {
    throw new UnathorizedError("User does not exist.");
  }

  const isPasswordValid = await user.comparePassword(password);
  if (!isPasswordValid) {
    throw new UnathorizedError("Incorrect password.");
  }

  const refreshToken = generateRefreshToken(user._id!.toString());
  const accessToken = generateAccessToken(refreshToken);

  user.refreshToken = await hashToken(refreshToken);
  await user.save({ validateBeforeSave: false });

  const {
    password: _,
    refreshToken: __,
    ...userWithoutSensetiveData
  } = user.toObject();
  return { user: userWithoutSensetiveData, accessToken, refreshToken };
}

export async function UserSignupService({
  fullName,
  username,
  password,
}: UserSignupInputType) {
  const existingUser = await User.findOne({ username });
  if (existingUser) {
    throw new ConflictError("Username already exists.");
  }
  const newUser = new User({ fullName, username, password });
  await newUser.save();

  const refreshToken = generateRefreshToken(newUser._id!.toString());
  const accessToken = generateAccessToken(refreshToken);

  newUser.refreshToken = await hashToken(refreshToken);
  await newUser.save({ validateBeforeSave: false });

  const {
    password: _,
    refreshToken: __,
    ...userWithoutSensetiveData
  } = newUser.toObject();

  return { user: userWithoutSensetiveData, accessToken, refreshToken };
}

export async function refreshUserToken(oldRefreshToken: string) {
  let decoded: { id: string };
  try {
    decoded = jwt.verify(oldRefreshToken, JWT_REFRESH_SECRET) as { id: string };
  } catch (error) {
    console.error(error);
    throw new UnathorizedError("Please log in again: Token not valid");
  }

  const user = await User.findById(decoded.id).select("+refreshToken");

  if (!user || !user.refreshToken) {
    throw new UnathorizedError("Please log in again: Token not found,");
  }

  const isValidToken = await user.compareToken(oldRefreshToken);

  if (!isValidToken) {
    user.refreshToken = undefined;
    await user.save({ validateBeforeSave: false });
    throw new UnathorizedError("Please log in again: Token reused or invalid.");
  }

  const newAccessToken = generateAccessToken(oldRefreshToken);
  const newRefreshToken = generateRefreshToken(user._id!.toString());

  user.refreshToken = await hashToken(newRefreshToken);
  await user.save({ validateBeforeSave: false });

  return { accessToken: newAccessToken, refreshToken: newRefreshToken };
}

export async function UserLogoutService(refreshToken: string): Promise<void> {
  try {
    // Verify the token to get the user ID
    const decoded = jwt.verify(refreshToken, JWT_REFRESH_SECRET) as {
      id: string;
    };

    // Clear the refresh token in the database
    await User.updateOne({ _id: decoded.id }, { $unset: { refreshToken: 1 } });
  } catch (error) {
    // If token is invalid, we still want to proceed with logout
    // but we can't identify which user to clear
    console.warn("Invalid token during logout:", error);
  }
}
