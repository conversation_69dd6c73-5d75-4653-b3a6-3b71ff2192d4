import type {
  UserSignupInputType,
  UserLoginInputType,
} from "../schema/user.schema";
import { User } from "../models/user.model";
import { ConflictError, UnathorizedError } from "../utils/error";
import {
  generateAccessToken,
  generateRefreshToken,
} from "../utils/generateToken";

export async function UserLoginService({
  username,
  password,
}: UserLoginInputType) {
  const user = await User.findOne({ username });
  if (!user) {
    throw new UnathorizedError("User does not exist.");
  }

  const isPasswordValid = await user.comparePassword(password);
  if (!isPasswordValid) {
    throw new UnathorizedError("Incorrect password.");
  }

  const refreshToken = generateRefreshToken(user._id!.toString());
  const accessToken = generateAccessToken(refreshToken);

  return { user: user.toObject(), token };
}

export async function UserSignupService({
  fullName,
  username,
  password,
}: UserSignupInputType) {
  const existingUser = await User.findOne({ username });
  if (existingUser) {
    throw new ConflictError("Username already exists.");
  }
  const newUser = new User({ fullName, username, password });

  await newUser.save();

  const token = generateToken(newUser._id!.toString());

  const userResponce = newUser.toObject();
  const { password: _, ...userWithoutPassword } = userResponce;

  return { user: userWithoutPassword, token };
}
