{"name": "second_brain", "module": "index.ts", "type": "module", "private": true, "scripts": {"dev": "bun run --watch index.ts", "build": "bun run index.ts"}, "devDependencies": {"@types/bun": "latest", "@types/jsonwebtoken": "^9.0.10", "@types/mongoose": "^5.11.97"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.0", "nanoid": "^5.1.5", "zod": "^3.25.67"}}