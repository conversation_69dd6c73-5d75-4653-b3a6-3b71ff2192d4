import { model, Schema, Types } from "mongoose";
import { nanoid } from "nanoid";

const LinkSchema = new Schema(
  {
    shortCode: {
      type: String,
      required: [true, "link code is required."],
      unique: true,
      default: nanoid(15),
      trim: true,
      index: true,
      lowercase: true,
    },
    contentId: {
      type: Types.ObjectId,
      ref: "Content",
      required: true,
      index: true,
    },
    userId: {
      type: Types.ObjectId,
      ref: "User",
      required: true,
      index: true,
    },
    expiresAt: {
      type: Date,
      validate: {
        validator: function (v?: Date) {
          if (v === null || v === undefined) return true;
          return v.getTime() > Date.now();
        },
        message: "Expiration date must be in future.",
      },
      index: true,
    },
    maxAccesses: {
      type: Number,
      min: [1, "Maximum accesses must be at least 1."],
    },
    currentAccesses: {
      type: Number,
      default: 0,
      min: [0, "Current accesses can not be negative."],
    },
    isActive: {
      type: Boolean,
      default: false,
      index: true,
    },
  },
  { timestamps: true },
);

export const Link = model("Link", LinkSchema);
