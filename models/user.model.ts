import bcrypt from "bcryptjs";
import { model, Schema, type Document } from "mongoose";

export interface IUser extends Document {
  fullName: string;
  username: string;
  password: string;
  refreshToken?: string;
  createdAt: Date;
  updatedAt: Date;
  comparePassword: (password: string) => Promise<boolean>;
  compareToken: (refreshToken: string) => Promise<boolean>;
}

// writing zod schema insted of this
/* function validatePassword(password: string): boolean {
  if (!/[a-z]/.test(password)) return false;
  if (!/[A-Z]/.test(password)) return false;
  if (!/[0-9]/.test(password)) return false;
  // if (!/[^a-zA-Z0-9]/.test(password)) return false;

  return true;
} */

const UserSchema = new Schema<IUser>(
  {
    fullName: {
      type: String,
      required: [true, "Full name is required"],
      trim: true,
      minlength: [3, "Full name must be at Least 3 letters"],
      maxlength: [50, "Full name must be at most 50 letters"],
    },
    username: {
      type: String,
      required: [true, "Username is required"],
      trim: true,
      lowercase: true,
      minlength: [3, "Username must be at Least 3 letters"],
      maxlength: [50, "Username must be at most 50 letters"],
      unique: true,
    },
    password: {
      type: String,
      required: [true, "Password is required"],
      trim: true,
      minlength: [8, "Password must be at Least 8 letters"],
      maxlength: [20, "Password must be at most 20 letters"],
      select: false,
      /*validate: {
        validator: validatePassword,
        msg: "Password must contain at least one uppercase, one lowercase and one number",
      }*/
    },
    refreshToken: {
      type: String,
      select: false,
    },
  },
  { timestamps: true },
);

UserSchema.pre<IUser>("save", async function (next) {
  if (this.isModified("password")) {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  }
});

UserSchema.methods.comparePassword = async function (
  currentPassword: string,
): Promise<boolean> {
  if (!this.password) {
    return false;
  }
  return await bcrypt.compare(currentPassword, this.password);
};

UserSchema.methods.compareToken = async function (
  token: string,
): Promise<boolean> {
  if (!this.refreshToken) {
    return false;
  }
  return bcrypt.compare(token, this.refreshToken);
};

export const User = model<IUser>("User", UserSchema);
