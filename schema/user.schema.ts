import { z } from "zod/v4";

export const UserSignupSchema = z.object({
  fullName: z
    .string()
    .trim()
    .min(1, "Full Name is required.")
    .min(3, "Full name must be at least 3 characters long.")
    .max(50, "Full name must be at most 50 characters long.")
    .regex(
      /^[a-zA-Z\s.-]+$/,
      "Full name can only contain letters, spaces, hyphens, and periods.",
    ),

  username: z
    .string()
    .trim()
    .min(1, "Username is required.")
    .min(3, "Username must be at least 3 charaters long.")
    .max(20, "Username must be at most 20 characters long.")
    .lowercase()
    .regex(
      /^[a-z0-9_.-]+$/,
      "Username can only contain lowercase letters, numbers, underscores, hyphens, and periods.",
    ),

  password: z
    .string()
    .trim()
    .min(1, "Password is required.")
    .min(8, "Password must be at least 8 charaters long.")
    .max(20, "Password must be at most 20 charaters long.")
    .refine(
      (password) => /[a-z]/.test(password),
      "Password must contain at least one lowercase letter.",
    )
    .refine(
      (password) => /[A-Z]/.test(password),
      "Password must contain at least one uppercase letter.",
    )
    .refine(
      (password) => /[0-9]/.test(password),
      "Password must contain at least one number.",
    )
    .refine(
      (password) => /[^a-zA-Z0-9]/.test(password),
      "Password must contain at least one symbol (e.g., !, @, #, $).",
    ),
});

export type UserSignupInputType = z.infer<typeof UserSignupSchema>;

export const UserLoginSchema = z.object({
  username: z
    .string()
    .trim()
    .min(1, "Username is required.")
    .min(3, "Username must be at least 3 charaters long.")
    .max(20, "Username must be at most 20 characters long.")
    .lowercase()
    .regex(
      /^[a-z0-9_.-]+$/,
      "Username can only contain lowercase letters, numbers, underscores, hyphens, and periods.",
    ),

  password: z
    .string()
    .trim()
    .min(1, "Password is required.")
    .min(8, "Password must be at least 8 charaters long.")
    .max(20, "Password must be at most 20 charaters long."),
});

export type UserLoginInputType = z.infer<typeof UserLoginSchema>;
