import { z } from "zod/v4";

const CONTENT_TYPES = ["image", "video", "artical", "audio"] as const;

export const ContentSchema = z.object({
  link: z.string().min(1, "Link is required.").url("Link must be a valid URL."),
  type: z.enum(
    CONTENT_TYPES,
    `Invalid content type. Must be one of: ${CONTENT_TYPES.join(",")}`,
  ),

  title: z
    .string()
    .min(1, "Title is required.")
    .min(3, "Title must be at least 3 character long.")
    .max(150, "Title must not exceed 150 characters.")
    .trim(),

  tags: z
    .array(
      z
        .string()
        .regex(
          /^[0-9a-fA-F]{24}$/,
          "Invalid tag ID format. Must be a 24-character hexadecimal string.",
        ),
    )
    .optional()
    .default([]),

  userId: z
    .string()
    .min(1, "User ID is required.")
    .regex(
      /^[0-9a-fA-F]{24}$/,
      "Invalid user ID format. Must be a 24-character hexadecimal string.",
    ),
});

export type ContentTypes = z.infer<typeof ContentSchema>;
