#!/usr/bin/env node

/**
 * Server startup script with health checks
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Second Brain Server...\n');

// Start the server
const serverProcess = spawn('bun', ['run', 'src/server.ts'], {
  stdio: 'inherit',
  cwd: process.cwd()
});

// Handle server process events
serverProcess.on('error', (error) => {
  console.error('❌ Failed to start server:', error.message);
  process.exit(1);
});

serverProcess.on('close', (code) => {
  console.log(`\n📊 Server process exited with code ${code}`);
  process.exit(code);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server gracefully...');
  serverProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down server gracefully...');
  serverProcess.kill('SIGTERM');
});

// Health check after startup
setTimeout(async () => {
  try {
    console.log('\n🔍 Performing health check...');
    const response = await fetch('http://localhost:3000/api/status');
    if (response.ok) {
      console.log('✅ Server is healthy and responding');
      console.log('\n📚 API Documentation: ./API_DOCUMENTATION.md');
      console.log('🧪 Test Routes: node test-routes.js');
      console.log('🌐 Server URL: http://localhost:3000');
    } else {
      console.log('⚠️ Server responded but with error status:', response.status);
    }
  } catch (error) {
    console.log('⚠️ Health check failed:', error.message);
  }
}, 3000); // Wait 3 seconds for server to start
